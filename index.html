<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Tree Model Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000;
            font-family: Arial, sans-serif;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 100;
            max-width: 300px;
        }
        
        #controls h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        #controls p {
            margin: 5px 0;
            line-height: 1.4;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
        
        .hidden {
            display: none;
        }
        
        #stats {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="loading">
            Loading 3D Tree Model...
        </div>
        
        <div id="controls">
            <h3>Camera Controls</h3>
            <p><strong>Mouse:</strong> Click and drag to look around</p>
            <p><strong>WASD:</strong> Move forward/back/left/right</p>
            <p><strong>Space/Shift:</strong> Move up/down</p>
            <p><strong>Q/E:</strong> Rotate left/right</p>
            <p><strong>Mouse Wheel:</strong> Zoom in/out</p>
            <p><strong>R:</strong> Reset camera position</p>
        </div>
        
        <div id="stats">
            <div>FPS: <span id="fps">0</span></div>
            <div>Triangles: <span id="triangles">0</span></div>
            <div>Camera: <span id="camera-pos">0, 0, 0</span></div>
        </div>
    </div>
    
    <!-- Three.js with multiple fallbacks -->
    <script>
        // Fallback loader system
        function loadScript(urls, callback) {
            let index = 0;
            function tryNext() {
                if (index >= urls.length) {
                    console.error('All CDN sources failed for:', urls);
                    callback(false);
                    return;
                }

                const script = document.createElement('script');
                script.src = urls[index];
                script.onload = () => callback(true);
                script.onerror = () => {
                    console.warn('Failed to load:', urls[index]);
                    index++;
                    tryNext();
                };
                document.head.appendChild(script);
            }
            tryNext();
        }

        // Load Three.js with fallbacks
        const threeUrls = [
            'https://cdnjs.cloudflare.com/ajax/libs/three.js/r152/three.min.js',
            'https://unpkg.com/three@0.152.2/build/three.min.js',
            'https://cdn.jsdelivr.net/npm/three@0.152.2/build/three.min.js'
        ];

        loadScript(threeUrls, (success) => {
            if (success) {
                console.log('Three.js loaded successfully');
                loadLoaders();
            } else {
                console.error('Failed to load Three.js from all sources');
            }
        });

        function loadLoaders() {
            // Try to load OBJ and MTL loaders
            const objUrls = [
                'https://unpkg.com/three@0.152.2/examples/js/loaders/OBJLoader.js',
                'https://cdn.jsdelivr.net/npm/three@0.152.2/examples/js/loaders/OBJLoader.js',
                'https://threejs.org/examples/js/loaders/OBJLoader.js'
            ];

            const mtlUrls = [
                'https://unpkg.com/three@0.152.2/examples/js/loaders/MTLLoader.js',
                'https://cdn.jsdelivr.net/npm/three@0.152.2/examples/js/loaders/MTLLoader.js',
                'https://threejs.org/examples/js/loaders/MTLLoader.js'
            ];

            loadScript(objUrls, (objSuccess) => {
                loadScript(mtlUrls, (mtlSuccess) => {
                    if (objSuccess && mtlSuccess) {
                        console.log('All loaders loaded successfully');
                        loadCameraController();
                    } else {
                        console.warn('Some loaders failed, will try to continue anyway');
                        loadCameraController();
                    }
                });
            });
        }

        function loadCameraController() {
            const script = document.createElement('script');
            script.src = 'CameraController.js';
            script.onload = () => {
                console.log('CameraController loaded');
                loadMainScript();
            };
            script.onerror = () => {
                console.error('Failed to load CameraController');
            };
            document.head.appendChild(script);
        }

        function loadMainScript() {
            const script = document.createElement('script');
            script.src = 'main.js';
            script.onload = () => {
                console.log('Main script loaded');
                initializeApp();
            };
            script.onerror = () => {
                console.error('Failed to load main script');
            };
            document.head.appendChild(script);
        }

        function initializeApp() {
            // Wait a bit to ensure everything is ready
            setTimeout(() => {
                console.log('Attempting to initialize TreeModelViewer...');
                if (typeof TreeModelViewer !== 'undefined') {
                    new TreeModelViewer();
                } else {
                    console.error('TreeModelViewer not defined');
                }
            }, 100);
        }
    </script>
</body>
</html>
