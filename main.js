import * as THREE from 'three';
import { OBJLoader } from 'three/addons/loaders/OBJLoader.js';
import { MTLLoader } from 'three/addons/loaders/MTLLoader.js';
import { CameraController } from './CameraController.js';

class TreeModelViewer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cameraController = null;
        this.treeModel = null;
        this.clock = new THREE.Clock();
        
        // Performance tracking
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        
        this.init();
    }
    
    init() {
        this.setupScene();
        this.setupLighting();
        this.setupRenderer();
        this.setupCamera();
        this.setupEventListeners();
        this.loadTreeModel();
        this.animate();
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue background
        
        // Add fog for depth perception
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
    }
    
    setupLighting() {
        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
        
        // Additional fill light
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-30, 20, -30);
        this.scene.add(fillLight);
    }
    
    setupRenderer() {
        const canvas = document.getElementById('canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas, 
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Position camera to get a good view of the tree
        this.camera.position.set(0, 10, 20);
        
        // Initialize camera controller
        this.cameraController = new CameraController(
            this.camera, 
            this.renderer.domElement,
            new THREE.Vector3(0, 10, 20)
        );
    }
    
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize(), false);
        
        // Reset camera with R key
        document.addEventListener('keydown', (event) => {
            if (event.code === 'KeyR') {
                this.cameraController.reset();
            }
        });
    }
    
    async loadTreeModel() {
        const loadingElement = document.getElementById('loading');
        
        try {
            // Create loaders
            const mtlLoader = new MTLLoader();
            const objLoader = new OBJLoader();
            
            // Set the path for textures
            mtlLoader.setPath('./TREEMODELTEST/');
            
            // Load materials first
            const materials = await new Promise((resolve, reject) => {
                mtlLoader.load('Tree.mtl', resolve, undefined, reject);
            });
            
            // Preload materials
            materials.preload();
            
            // Set materials to OBJ loader
            objLoader.setMaterials(materials);
            objLoader.setPath('./TREEMODELTEST/');
            
            // Load the OBJ model
            const object = await new Promise((resolve, reject) => {
                objLoader.load('Tree.obj', resolve, undefined, reject);
            });
            
            // Process the loaded model
            this.treeModel = object;
            
            // Enable shadows and optimize materials
            this.treeModel.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                    
                    // Ensure materials are properly configured
                    if (child.material) {
                        child.material.side = THREE.FrontSide;
                        if (child.material.map) {
                            child.material.map.wrapS = THREE.RepeatWrapping;
                            child.material.map.wrapT = THREE.RepeatWrapping;
                        }
                    }
                }
            });
            
            // Center and scale the model if needed
            const box = new THREE.Box3().setFromObject(this.treeModel);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());
            
            // Move model to origin
            this.treeModel.position.sub(center);
            
            // Scale if too large (optional)
            const maxDimension = Math.max(size.x, size.y, size.z);
            if (maxDimension > 20) {
                const scale = 20 / maxDimension;
                this.treeModel.scale.setScalar(scale);
            }
            
            this.scene.add(this.treeModel);
            
            // Hide loading message
            loadingElement.classList.add('hidden');
            
            console.log('Tree model loaded successfully!');
            console.log('Model size:', size);
            console.log('Model center:', center);
            
        } catch (error) {
            console.error('Error loading tree model:', error);
            loadingElement.textContent = 'Error loading model: ' + error.message;
        }
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    updateStats() {
        // Update FPS
        this.frameCount++;
        const currentTime = performance.now();
        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            document.getElementById('fps').textContent = this.fps;
        }
        
        // Update triangle count
        let triangles = 0;
        this.scene.traverse((object) => {
            if (object.isMesh && object.geometry) {
                if (object.geometry.index) {
                    triangles += object.geometry.index.count / 3;
                } else {
                    triangles += object.geometry.attributes.position.count / 3;
                }
            }
        });
        document.getElementById('triangles').textContent = Math.round(triangles);
        
        // Update camera position
        const pos = this.camera.position;
        document.getElementById('camera-pos').textContent = 
            `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        // Update camera controller
        if (this.cameraController) {
            this.cameraController.update();
        }
        
        // Update stats
        this.updateStats();
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TreeModelViewer();
});
