// THREE is loaded via script tags
// Need to load OBJ and MTL loaders

// Debug: Check if everything is loaded
console.log('THREE:', typeof THREE);
console.log('THREE.MTLLoader:', typeof THREE.MTLLoader);
console.log('THREE.OBJLoader:', typeof THREE.OBJLoader);
console.log('CameraController:', typeof CameraController);
console.log('PointCloudGenerator:', typeof PointCloudGenerator);

class TreeModelViewer {
    constructor() {
        console.log('TreeModelViewer constructor called');
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cameraController = null;
        this.treePointCloud = null;
        this.pointCloudGenerator = new PointCloudGenerator();
        this.clock = new THREE.Clock();

        // Performance tracking
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;

        this.init();
    }
    
    init() {
        this.setupScene();
        this.setupLighting();
        this.setupRenderer();
        this.setupCamera();
        this.setupEventListeners();
        this.generateTreePointCloud();
        this.animate();
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue background
        
        // Add fog for depth perception
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
    }
    
    setupLighting() {
        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
        
        // Additional fill light
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-30, 20, -30);
        this.scene.add(fillLight);
    }
    
    setupRenderer() {
        const canvas = document.getElementById('canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas, 
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Position camera to get a good view of the tree
        this.camera.position.set(0, 10, 20);
        
        // Initialize camera controller
        this.cameraController = new CameraController(
            this.camera, 
            this.renderer.domElement,
            new THREE.Vector3(0, 10, 20)
        );
    }
    
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize(), false);
        
        // Reset camera with R key
        document.addEventListener('keydown', (event) => {
            if (event.code === 'KeyR') {
                this.cameraController.reset();
            }
        });

        // Add button event listeners
        const regenerateBtn = document.getElementById('regenerate-btn');
        const testBtn = document.getElementById('test-btn');

        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => {
                console.log('Manual regeneration triggered');
                // Remove existing pointcloud
                if (this.treePointCloud) {
                    this.scene.remove(this.treePointCloud);
                }
                // Show loading again
                document.getElementById('loading').classList.remove('hidden');
                document.getElementById('loading').classList.remove('loading-error');
                // Regenerate
                this.generateTreePointCloud();
            });
        }

        if (testBtn) {
            testBtn.addEventListener('click', () => {
                console.log('Manual file test triggered');
                this.testFileLoading();
            });
        }
    }
    
    async generateTreePointCloud() {
        const loadingElement = document.getElementById('loading');
        const loadingText = document.getElementById('loading-text');
        const loadingBar = document.getElementById('loading-bar');
        const loadingDetails = document.getElementById('loading-details');

        // Set up progress callback
        this.pointCloudGenerator.setProgressCallback((progress, message) => {
            loadingBar.style.width = `${progress}%`;
            loadingDetails.textContent = message;
            console.log(`Progress: ${progress}% - ${message}`);
        });

        try {
            console.log('Starting pointcloud generation...');

            // Check if required loaders are available
            if (typeof THREE.OBJLoader === 'undefined') {
                throw new Error('OBJLoader not available');
            }
            if (typeof THREE.MTLLoader === 'undefined') {
                throw new Error('MTLLoader not available');
            }

            // Generate the pointcloud with configurable point count
            const targetPoints = this.getOptimalPointCount();
            console.log(`Generating ${targetPoints} points...`);

            loadingText.textContent = 'Generating Tree Pointcloud...';
            loadingDetails.textContent = 'Initializing...';

            // Add timeout to prevent hanging
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Generation timeout after 30 seconds')), 30000);
            });

            const generationPromise = this.pointCloudGenerator.generatePointCloud(
                './TREEMODELTEST/Tree.obj',
                './TREEMODELTEST/Tree.mtl',
                targetPoints
            );

            this.treePointCloud = await Promise.race([generationPromise, timeoutPromise]);

            // Center and scale the pointcloud if needed
            const box = new THREE.Box3().setFromObject(this.treePointCloud);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());

            // Move pointcloud to origin
            this.treePointCloud.position.sub(center);

            // Scale if too large (optional)
            const maxDimension = Math.max(size.x, size.y, size.z);
            if (maxDimension > 20) {
                const scale = 20 / maxDimension;
                this.treePointCloud.scale.setScalar(scale);
            }

            this.scene.add(this.treePointCloud);

            // Verify the pointcloud was added
            console.log('Scene children after adding pointcloud:', this.scene.children.length);
            console.log('Pointcloud in scene:', this.scene.getObjectByName('TreePointCloud'));

            // Make sure camera can see the pointcloud
            this.camera.lookAt(center);

            // Hide loading message after a brief delay to show completion
            setTimeout(() => {
                loadingElement.classList.add('hidden');
            }, 1000);

            console.log('Tree pointcloud generated successfully!');
            console.log('Pointcloud size:', size);
            console.log('Pointcloud center:', center);
            console.log('Point count:', this.treePointCloud.geometry.attributes.position.count);
            console.log('Pointcloud visible:', this.treePointCloud.visible);
            console.log('Pointcloud position:', this.treePointCloud.position);

        } catch (error) {
            console.error('Error generating tree pointcloud:', error);

            // Show error state
            loadingElement.classList.add('loading-error');
            loadingText.textContent = 'Failed to Generate Pointcloud';
            loadingDetails.textContent = error.message;
            loadingBar.style.width = '100%';

            // Try to create a simple test pointcloud as fallback
            console.log('Creating test pointcloud as fallback...');
            this.createTestPointCloud();

            // Also try a simple file load test
            this.testFileLoading();

            // Auto-hide error after 5 seconds
            setTimeout(() => {
                loadingElement.classList.add('hidden');
            }, 5000);
        }
    }

    createTestPointCloud() {
        // Create a simple test pointcloud to verify rendering works
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(3000); // 1000 points
        const colors = new Float32Array(3000);

        for (let i = 0; i < 1000; i++) {
            // Random positions in a cube
            positions[i * 3] = (Math.random() - 0.5) * 10;
            positions[i * 3 + 1] = Math.random() * 10;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 10;

            // Green colors
            colors[i * 3] = 0.2 + Math.random() * 0.3; // R
            colors[i * 3 + 1] = 0.5 + Math.random() * 0.5; // G
            colors[i * 3 + 2] = 0.1 + Math.random() * 0.3; // B
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            sizeAttenuation: true
        });

        const testPointCloud = new THREE.Points(geometry, material);
        testPointCloud.name = 'TestPointCloud';
        this.scene.add(testPointCloud);

        console.log('Test pointcloud created and added to scene');
    }

    async testFileLoading() {
        console.log('Testing file loading...');
        try {
            // Test if we can fetch the files directly
            const mtlResponse = await fetch('./TREEMODELTEST/Tree.mtl');
            if (!mtlResponse.ok) {
                console.error('MTL file not accessible:', mtlResponse.status);
            } else {
                console.log('MTL file accessible');
                const mtlText = await mtlResponse.text();
                console.log('MTL content preview:', mtlText.substring(0, 200));
            }

            const objResponse = await fetch('./TREEMODELTEST/Tree.obj');
            if (!objResponse.ok) {
                console.error('OBJ file not accessible:', objResponse.status);
            } else {
                console.log('OBJ file accessible');
                const objText = await objResponse.text();
                console.log('OBJ content preview:', objText.substring(0, 200));
            }
        } catch (error) {
            console.error('File loading test failed:', error);
        }
    }

    getOptimalPointCount() {
        // Determine optimal point count based on device capabilities
        const canvas = this.renderer.domElement;
        const context = this.renderer.getContext();

        // Check WebGL capabilities
        const maxVertexAttribs = context.getParameter(context.MAX_VERTEX_ATTRIBS);
        const maxTextureSize = context.getParameter(context.MAX_TEXTURE_SIZE);

        // Estimate device performance level
        const pixelRatio = window.devicePixelRatio || 1;
        const screenPixels = window.innerWidth * window.innerHeight * pixelRatio;

        let targetPoints;

        if (screenPixels > 2000000) { // High-res displays
            targetPoints = 300000; // More points for better quality
        } else if (screenPixels > 1000000) { // Standard displays
            targetPoints = 200000; // Balanced quality/performance
        } else { // Lower-res or mobile
            targetPoints = 100000; // Prioritize performance
        }

        console.log(`Screen pixels: ${screenPixels}, Target points: ${targetPoints}`);
        return targetPoints;
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    updateStats() {
        // Update FPS
        this.frameCount++;
        const currentTime = performance.now();
        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            document.getElementById('fps').textContent = this.fps;
        }
        
        // Update point count
        let points = 0;
        this.scene.traverse((object) => {
            if (object.isPoints && object.geometry) {
                points += object.geometry.attributes.position.count;
            } else if (object.isMesh && object.geometry) {
                if (object.geometry.index) {
                    points += object.geometry.index.count / 3;
                } else {
                    points += object.geometry.attributes.position.count / 3;
                }
            }
        });
        document.getElementById('triangles').textContent = Math.round(points);
        
        // Update camera position
        const pos = this.camera.position;
        document.getElementById('camera-pos').textContent = 
            `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        // Update camera controller
        if (this.cameraController) {
            this.cameraController.update();
        }
        
        // Update stats
        this.updateStats();
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// TreeModelViewer class is now available globally
// Initialization is handled in HTML
