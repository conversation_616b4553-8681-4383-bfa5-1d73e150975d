// Simple Tree Model Viewer
console.log('Loading Simple Tree Model Viewer...');

class SimpleTreeViewer {
    constructor() {
        console.log('SimpleTreeViewer constructor called');
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cameraController = null;
        this.treeModel = null;
        this.clock = new THREE.Clock();
        
        this.init();
    }
    
    init() {
        console.log('Initializing SimpleTreeViewer...');
        this.setupScene();
        this.setupLighting();
        this.setupRenderer();
        this.setupCamera();
        this.setupControls();
        this.createSimpleTree();
        this.animate();
        console.log('SimpleTreeViewer initialization complete');
    }
    
    setupScene() {
        console.log('Setting up scene...');
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue background
        
        // Add fog for depth perception
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        console.log('Scene setup complete');
    }
    
    setupLighting() {
        console.log('Setting up lighting...');
        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
        console.log('Lighting setup complete');
    }
    
    setupRenderer() {
        console.log('Setting up renderer...');
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Add to DOM
        const container = document.getElementById('container');
        if (container) {
            container.appendChild(this.renderer.domElement);
            console.log('Renderer added to container');
        } else {
            document.body.appendChild(this.renderer.domElement);
            console.log('Renderer added to body');
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
        console.log('Renderer setup complete');
    }
    
    setupCamera() {
        console.log('Setting up camera...');
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(15, 8, 15);
        this.camera.lookAt(0, 6, 0);
        console.log('Camera setup complete. Position:', this.camera.position);
    }
    
    setupControls() {
        console.log('Setting up controls...');
        if (typeof CameraController !== 'undefined') {
            this.cameraController = new CameraController(this.camera, this.renderer.domElement);
            console.log('CameraController initialized');
        } else {
            console.warn('CameraController not available, using basic controls');
            // Basic mouse controls fallback
            this.setupBasicControls();
        }
    }
    
    setupBasicControls() {
        console.log('Setting up basic mouse controls...');
        let isMouseDown = false;
        let mouseX = 0;
        let mouseY = 0;
        
        this.renderer.domElement.addEventListener('mousedown', (event) => {
            isMouseDown = true;
            mouseX = event.clientX;
            mouseY = event.clientY;
        });
        
        this.renderer.domElement.addEventListener('mouseup', () => {
            isMouseDown = false;
        });
        
        this.renderer.domElement.addEventListener('mousemove', (event) => {
            if (!isMouseDown) return;
            
            const deltaX = event.clientX - mouseX;
            const deltaY = event.clientY - mouseY;
            
            // Rotate camera around the tree
            const spherical = new THREE.Spherical();
            spherical.setFromVector3(this.camera.position);
            spherical.theta -= deltaX * 0.01;
            spherical.phi += deltaY * 0.01;
            spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));
            
            this.camera.position.setFromSpherical(spherical);
            this.camera.lookAt(0, 6, 0);
            
            mouseX = event.clientX;
            mouseY = event.clientY;
        });
        
        console.log('Basic controls setup complete');
    }
    
    createSimpleTree() {
        console.log('Creating simple tree...');
        
        // Create a tree group
        const treeGroup = new THREE.Group();
        treeGroup.name = 'SimpleTree';
        
        // Trunk
        console.log('Creating trunk...');
        const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8, 8);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 4;
        trunk.castShadow = true;
        trunk.receiveShadow = true;
        treeGroup.add(trunk);
        
        // Foliage (multiple spheres)
        console.log('Creating foliage...');
        const foliageMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 }); // Forest green
        
        // Bottom layer
        const foliage1 = new THREE.Mesh(new THREE.SphereGeometry(3, 8, 6), foliageMaterial);
        foliage1.position.y = 6;
        foliage1.castShadow = true;
        treeGroup.add(foliage1);
        
        // Middle layer
        const foliage2 = new THREE.Mesh(new THREE.SphereGeometry(2.5, 8, 6), foliageMaterial);
        foliage2.position.y = 8.5;
        foliage2.castShadow = true;
        treeGroup.add(foliage2);
        
        // Top layer
        const foliage3 = new THREE.Mesh(new THREE.SphereGeometry(2, 8, 6), foliageMaterial);
        foliage3.position.y = 10.5;
        foliage3.castShadow = true;
        treeGroup.add(foliage3);
        
        // Add ground plane
        console.log('Creating ground...');
        const groundGeometry = new THREE.PlaneGeometry(50, 50);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 }); // Light green
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        // Add to scene
        this.scene.add(treeGroup);
        this.treeModel = treeGroup;
        
        console.log('Simple tree created and added to scene');
        console.log('Tree model position:', treeGroup.position);
        console.log('Scene children count:', this.scene.children.length);
        
        // Hide loading screen
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.add('hidden');
            console.log('Loading screen hidden');
        }
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // Update camera controller if available
        if (this.cameraController && this.cameraController.update) {
            this.cameraController.update(deltaTime);
        }
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing SimpleTreeViewer...');
    
    // Check if THREE.js is available
    if (typeof THREE === 'undefined') {
        console.error('THREE.js not loaded!');
        return;
    }
    
    console.log('THREE.js version:', THREE.REVISION);
    
    // Initialize the viewer
    window.treeViewer = new SimpleTreeViewer();
    console.log('SimpleTreeViewer initialized and stored in window.treeViewer');
});

console.log('Simple Tree Model Viewer script loaded');
